package com.zjjcnt.project.ck.zzj.third.rop;

import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.util.JsonUtils;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.third.rop.req.YztyCrjRopReq;
import com.zjjcnt.project.ck.zzj.third.rop.req.YztyJjRopReq;
import com.zjjcnt.project.ck.zzj.third.rop.resp.RopErrorResp;
import com.zjjcnt.project.ck.zzj.third.rop.resp.YztyCrjRopResp;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.Objects;

/**
 * 终端办证设备办理跨省身份证rop接口
 * <AUTHOR>
 * @date 2024-10-14 11:10:00
 */
@Slf4j
@Component
public class YztyCrjRopClient {

    @Value("${zjjcnt.rop.yzty.app-key:czrkzpcrj}")
    private String appKey;

    @Value("${zjjcnt.rop.yzty.url}")
    private String serverUrl;

    private static final String RESULT_CODE_SUCCESS = "1";
    private RestTemplate restTemplate;

    private static final long READ_TIMEOUT_SECONDS = 20L;
    private static final long CONNECT_TIMEOUT_SECONDS = 2L;

    private static final String METHOD_CRJ = "processYztyCrj";
    private static final String METHOD_JJ = "processYztyJj";

    @PostConstruct
    public void init() {
        restTemplate = new RestTemplateBuilder()
                .readTimeout(Duration.ofSeconds(READ_TIMEOUT_SECONDS))
                .connectTimeout(Duration.ofSeconds(CONNECT_TIMEOUT_SECONDS))
                .build();
    }

    /**
     * 一照通用-出入境照片上传
     * @param yztyCrjRopReq req
     */
    public void processYztyCrj(YztyCrjRopReq yztyCrjRopReq) {
        MultiValueMap<String, String> form = new LinkedMultiValueMap<>();
        form.add("appKey", appKey);
        form.add("v", "1.0");
        form.add("format", "json");
        form.add("method", METHOD_CRJ);
        form.add("gmsfhm", yztyCrjRopReq.getGmsfhm());
        form.add("xtmc", yztyCrjRopReq.getXtmc());
        form.add("base64crj", yztyCrjRopReq.getBase64crj());
        form.add("base64mp", yztyCrjRopReq.getBase64mp());
        form.add("sfrgqztg", yztyCrjRopReq.getSfrgqztg());
        form.add("psrq", yztyCrjRopReq.getPsrq());
        form.add("cjzdbm", yztyCrjRopReq.getCjzdbm());
        form.add("czyid", yztyCrjRopReq.getCzyid());
        form.add("czyxm", yztyCrjRopReq.getCzyxm());
        form.add("sbid", yztyCrjRopReq.getSbid());
        form.add("sjly", "ckxc");

        String result = restTemplate.postForObject(serverUrl, form, String.class);
        YztyCrjRopResp response = JsonUtils.parseObject(result, YztyCrjRopResp.class);
        assert response != null;
        if (Objects.isNull(response.getRetcode())) {
            RopErrorResp errorResponse = JsonUtils.parseObject(result, RopErrorResp.class);
            log.error("调用一照通用出入境照片上传rop接口错误, method={}, code={}, message={}, solution={}", METHOD_CRJ,
                    errorResponse.getCode(), errorResponse.getMessage(), errorResponse.getSolution());
            throw new ServiceException(CkZzjErrorCode.YZTY_CRJ_ROP_API_ERROR);
        } else if (!RESULT_CODE_SUCCESS.equals(response.getRetcode())) {
            log.error("调用一照通用出入境照片上传rop接口错误, method={}, retcod={}, retmsg={}", METHOD_CRJ, response.getRetcode(), response.getRetmsg());
            throw new ServiceException(CkZzjErrorCode.YZTY_CRJ_ROP_API_ERROR, response.getRetmsg());
        }
    }


    /**
     * 一照通用-交警照片上传
     * @param yztyJjRopReq req
     */
    public void processYztyJj(YztyJjRopReq yztyJjRopReq) {
        MultiValueMap<String, String> form = new LinkedMultiValueMap<>();
        form.add("appKey", appKey);
        form.add("v", "1.0");
        form.add("format", "json");
        form.add("method", METHOD_JJ);
        form.add("gmsfhm", yztyJjRopReq.getGmsfhm());
        form.add("base64jj", yztyJjRopReq.getBase64jj());
        form.add("xb", yztyJjRopReq.getXb());
        form.add("czyid", yztyJjRopReq.getCzyid());
        form.add("czyxm", yztyJjRopReq.getCzyxm());
        form.add("czygmsfhm", yztyJjRopReq.getCzygmsfhm());
        form.add("czyip", yztyJjRopReq.getCzyip());
        form.add("pcs", yztyJjRopReq.getPcs());
        form.add("pcsmc", yztyJjRopReq.getPcsmc());
        form.add("sbid", yztyJjRopReq.getSbid());
        form.add("sjly", "ckxc");

        String result = restTemplate.postForObject(serverUrl, form, String.class);
        YztyCrjRopResp response = JsonUtils.parseObject(result, YztyCrjRopResp.class);
        assert response != null;
        if (Objects.isNull(response.getRetcode())) {
            RopErrorResp errorResponse = JsonUtils.parseObject(result, RopErrorResp.class);
            log.error("调用一照通用交警照片上传rop接口错误, method={}, code={}, message={}, solution={}", METHOD_JJ,
                    errorResponse.getCode(), errorResponse.getMessage(), errorResponse.getSolution());
            throw new ServiceException(CkZzjErrorCode.YZTY_JJ_ROP_API_ERROR);
        } else if (!RESULT_CODE_SUCCESS.equals(response.getRetcode())) {
            log.error("调用一照通用交警照片上传rop接口错误, method={}, retcod={}, retmsg={}", METHOD_JJ, response.getRetcode(), response.getRetmsg());
            throw new ServiceException(CkZzjErrorCode.YZTY_JJ_ROP_API_ERROR, response.getRetmsg());
        }
    }

}
