package com.zjjcnt.project.ck.zzj.third.crj;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.util.JsonUtils;
import com.zjjcnt.project.ck.zzj.exception.CkZzjErrorCode;
import com.zjjcnt.project.ck.zzj.third.crj.req.CrjQueryPeopleNewReq;
import com.zjjcnt.project.ck.zzj.third.crj.req.CrjSavePaperPhotoReq;
import com.zjjcnt.project.ck.zzj.third.crj.resp.CertificateInfoResp;
import com.zjjcnt.project.ck.zzj.third.crj.resp.CrjCommonResp;
import com.zjjcnt.project.ck.zzj.third.crj.resp.CrjPeopleInfo;
import com.zjjcnt.project.ck.zzj.third.crj.resp.CrjQueryPeopleNewResp;
import com.zjjcnt.project.ck.zzj.util.CkHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 出入境接口客户端
 *
 * <AUTHOR>
 * @date 2024-08-08 17:09:00
 */
@Slf4j
@Component
public class CrjApiClient {

    private static final String FUNC_CODE_SAVE_PAPER_PHOTO = "sq_savePaperPhoto";
    private static final String FUNC_CODE_QUERY_PEOPLE_NEW = "qg_queryPeoplenew";
    private static final String URL_GET_CERTIFICATE_INFO = "http://127.0.0.1:7788/GetCertificateInfoPKCS7";

    private volatile Eecmisws eecmisws;
    private EecmiswsPortType portType;

    /**
     * 新全国人口库查询接口
     * @param crjQueryPeopleNewReq
     * @return
     */
    public CrjPeopleInfo queryPeople(CrjQueryPeopleNewReq crjQueryPeopleNewReq) {
        String signData = getCertificateInfo();
        String sessionId = login(signData);
        CrjQueryPeopleNewResp crjQueryPeopleNewResp = uniMethod(sessionId, FUNC_CODE_QUERY_PEOPLE_NEW,
                JsonUtils.toJsonString(crjQueryPeopleNewReq), CrjQueryPeopleNewResp.class);

        if (!Boolean.TRUE.equals(crjQueryPeopleNewResp.getIsRight())) {
            String errorMsg = String.format("人口信息查询失败, code=%s, message=%s", crjQueryPeopleNewResp.getReturnCode(),
                    crjQueryPeopleNewResp.getReturnCodeDes());
            log.error(errorMsg);
            throw new ServiceException(CkZzjErrorCode.CRJ_API_ERROR, errorMsg);
        }

        List<CrjPeopleInfo> returnInfos = crjQueryPeopleNewResp.getReturnInfos();

        if (!CollectionUtils.isEmpty(returnInfos)) {
            return returnInfos.get(0);
        }
        return null;
    }

    /**
     * 预采集制证相片入库接口
     * @param savePaperPhotoReq
     */
    public void savePaperPhoto(CrjSavePaperPhotoReq savePaperPhotoReq) {
        String signData = getCertificateInfo();
        String sessionId = login(signData);
        uniMethod(sessionId, FUNC_CODE_SAVE_PAPER_PHOTO, JsonUtils.toJsonString(savePaperPhotoReq), Void.class);
    }

    public String login(String signData) {
        String response = getPortType().loginNew(signData);

        CrjCommonResp<String> crjCommonResp = JsonUtils.parseObject(response, new TypeReference<CrjCommonResp<String>>() {
        });

        if (crjCommonResp.isSuccess()) {
            return crjCommonResp.getData();
        }
        String errorMsg = String.format("出入境接口认证失败, states=%s, message=%s", crjCommonResp.getState(),
                crjCommonResp.getMessage());
        log.error(errorMsg);
        throw new ServiceException(CkZzjErrorCode.CRJ_API_ERROR, errorMsg);
    }

    public <T> T uniMethod(String sessionId, String funccode, String args, Class<T> clazz) {
        String response = getPortType().unimethod(sessionId, funccode, args);

        CrjCommonResp crjCommonResp = JsonUtils.parseObject(response, CrjCommonResp.class);

        if (crjCommonResp.isSuccess()) {
            return JsonUtils.getAsObject(response, "data", clazz);
        }

        String errorMsg = String.format("出入境接口调用失败, funccode=%s, states=%s, message=%s", funccode,
                crjCommonResp.getState(), crjCommonResp.getMessage());
        log.error(errorMsg);
        throw new ServiceException(CkZzjErrorCode.CRJ_API_ERROR, errorMsg);
    }

    private String getCertificateInfo() {
        String cert = "";
        try {
            String result = CkHttpUtils.sendPost1(URL_GET_CERTIFICATE_INFO, null, null, null);
            if (null != result) {
                CertificateInfoResp certificateInfoResp = JsonUtils.parseObject(result, CertificateInfoResp.class);
                //调用成功
                if (1 == certificateInfoResp.getSuccess()) {
                    //获取签名数据
                    cert = certificateInfoResp.getData();
                }
            }
        } catch (Exception e) {
           log.error("获取探针程序签名数据失败", e);
        }
        return cert;
    }

    private EecmiswsPortType getPortType() {
        if (eecmisws == null) {
            synchronized (Eecmisws.class) {
                if (eecmisws == null) {
                    eecmisws = new Eecmisws();
                    portType = eecmisws.getEecmiswsHttpSoap11Endpoint();
                }
            }
        }
        return portType;
    }
}
