/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ADescriptions: typeof import('ant-design-vue/es')['Descriptions']
    ADescriptionsItem: typeof import('ant-design-vue/es')['DescriptionsItem']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    AImage: typeof import('ant-design-vue/es')['Image']
    AImagePreviewGroup: typeof import('ant-design-vue/es')['ImagePreviewGroup']
    AInput: typeof import('ant-design-vue/es')['Input']
    AModal: typeof import('ant-design-vue/es')['Modal']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    Camera: typeof import('./src/components/camera/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
