<script lang="ts" setup name="dashboard-index">
import type { TodoTaskStatisticsResponse } from '#/api/dashboard/dashboard';

import { computed, onMounted, onUnmounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { apiTodoTaskStatistics } from '#/api/dashboard/dashboard';

// 响应式数据
const loading = ref(false);
const error = ref<string>('');
const statisticsData = ref<TodoTaskStatisticsResponse>({});
const activeTab = ref('my-todo');

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null;

// 获取统计数据
const fetchStatistics = async () => {
  try {
    loading.value = true;
    error.value = '';
    const response = await apiTodoTaskStatistics();
    statisticsData.value = response || {};
    const data = statisticsData.value;
    data.hjCount = data.hjxxKsxzCount + data.hjxxKsxxCount;
    data.total = data.hjCount + data.hlwsbdclCount;
  } catch (error_) {
    error.value = '获取统计数据失败，请稍后重试';
    console.error('获取待办工作统计失败:', error_);
  } finally {
    loading.value = false;
  }
};

// 顶部导航选项卡数据
const navigationTabs = computed(() => [
  {
    id: 'my-todo',
    label: '我的待办',
    value: statisticsData.value.total,
  },
  {
    id: 'today-completed',
    label: '今日完成任务',
    value: '0',
  },
  {
    id: 'week-completed',
    label: '本周完成任务',
    value: '0',
  },
]);

// 卡片数据
const cardData = computed(() => {
  const data = statisticsData.value;
  return [
    {
      id: 'card-certificate-issuance',
      title: '制证信息',
      badge: '0',
      items: [
        { label: '待导入', count: 0 },
        { label: '待初审', count: 0 },
        { label: '质量回查', count: 0 },
        { label: '自助待核实', count: 0 },
        { label: '分局审核', count: 0 },
        { label: '待签发', count: 0 },
        { label: '临证审核', count: 0 },
        { label: '市局审核', count: 0 },
        { label: '跨省证发送', count: 0 },
        { label: '跨省证回馈', count: 0 },
        { label: '部临证审核', count: 0 },
        { label: '身份证网办', count: 0 },
        { label: '绿通分局审', count: 0 },
        { label: '绿通市局审', count: 0 },
      ],
    },
    {
      id: 'card-household-top',
      title: '户籍信息',
      badge: data.hjCount,
      items: [
        { label: '迁出处理(迁移)', count: 0 },
        { label: '迁出处理(变更)', count: 0 },
        { label: '迁出处理(恢复)', count: 0 },
        { label: '长三角跨省申报)', count: 0 },
        { label: '跨省协作', count: data.hjxxKsxzCount || 0 },
        { label: '跨省消息', count: data.hjxxKsxxCount || 0 },
        { label: '丧失国籍', count: 0 },
      ],
    },
    {
      id: 'card-national-approval',
      title: '全国通办和政务网审批',
      badge: data.hlwsbdclCount,
      items: [
        { label: '我受理的全城通办', count: 0 },
        { label: '我管辖的全城通办', count: 0 },
        { label: '全城通办民警审核', count: 0 },
        {
          label: '互联网申报待处理',
          count: data.hlwsbdclCount || 0,
        },
        { label: '政务大厅申报处理', count: 0 },
        { label: '我受理的政务大厅', count: 0 },
        { label: '我管辖的政务大厅', count: 0 },
        { label: '待延时受理的网件', count: 0 },
      ],
    },
    {
      id: 'card-household-bottom',
      title: '户籍信息地审批信息',
      badge: '0',
      items: [
        { label: '户籍受理待处理', count: 0 },
        { label: '社区民警', count: 0 },
        { label: '民警审核', count: 0 },
        { label: '所领导', count: 0 },
        { label: '分局民警', count: 0 },
        { label: '分局领导', count: 0 },
        { label: '部级处理', count: 0 },
        { label: '市局民警', count: 0 },
        { label: '市局科室', count: 0 },
        { label: '市局领导', count: 0 },
      ],
    },
  ];
});

// 启动自动刷新
const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  refreshTimer = setInterval(
    () => {
      fetchStatistics();
    },
    5 * 60 * 1000,
  );
};

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

// 重试函数
const retry = () => {
  fetchStatistics();
};

// 切换选项卡
const switchTab = (tabId: string) => {
  activeTab.value = tabId;
};

// 组件挂载时获取数据并启动自动刷新
onMounted(() => {
  fetchStatistics();
  startAutoRefresh();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh();
});
</script>

<template>
  <Page>
    <div class="p-6">
      <!-- 顶部统计展示 -->
      <div class="top-statistics mb-6">
        <div class="flex justify-center space-x-6">
          <div
            v-for="tab in navigationTabs"
            :key="tab.id"
            class="stat-item rounded-lg border border-gray-200 bg-white p-4 text-center shadow-sm"
          >
            <div class="text-sm font-medium text-gray-900">{{ tab.label }}</div>
            <div class="text-primary mt-1 text-lg font-bold">
              {{ tab.value }}
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div
          class="border-primary h-8 w-8 animate-spin rounded-full border-b-2"
        ></div>
        <span class="ml-2 text-gray-600">加载中...</span>
      </div>

      <!-- 错误状态 -->
      <div
        v-else-if="error"
        class="flex flex-col items-center justify-center py-12"
      >
        <div class="mb-4 text-center text-red-500">
          <svg
            class="mx-auto mb-2 h-12 w-12"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
          <p class="text-red-600">{{ error }}</p>
        </div>
        <button
          @click="retry"
          class="bg-primary hover:bg-primary/90 rounded-md px-4 py-2 text-white transition-colors"
        >
          重试
        </button>
      </div>

      <!-- 内容卡片网格 -->
      <div v-else class="grid grid-cols-1 gap-6 md:grid-cols-2">
        <a-card
          v-for="card in cardData"
          :key="card.id"
          :id="card.id"
          class="content-card"
          :title="card.title"
        >
          <template #extra>
            <span
              class="badge rounded-full bg-red-500 px-2 py-1 text-xs font-bold text-white"
            >
              {{ card.badge }}
            </span>
          </template>

          <div class="card-content">
            <ul class="card-list grid grid-cols-2 gap-2">
              <li
                v-for="item in card.items"
                :key="item.label"
                class="list-item"
              >
                <a
                  href="#"
                  class="item-link block rounded p-2 transition-colors hover:bg-gray-50"
                >
                  <span class="item-label text-sm text-gray-700">{{
                    item.label
                  }}</span>
                  <span
                    class="item-count text-primary ml-2 text-sm font-medium"
                  >
                    {{ item.count }}条
                  </span>
                </a>
              </li>
            </ul>
          </div>
        </a-card>
      </div>
    </div>
  </Page>
</template>

<style lang="scss" scoped>
.nav-tab {
  min-width: 120px;
  text-align: center;
}

.badge {
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
}

.content-card {
  min-height: 300px;
}

.card-list {
  max-height: 400px;
  overflow-y: auto;
}

.item-link:hover {
  text-decoration: none;
}
</style>
