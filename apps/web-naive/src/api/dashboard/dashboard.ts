import { requestClient } from '#/api/request';

/**
 * 待办工作统计数据接口
 */
export interface TodoTaskStatisticsResponse {
  /** 全国通办和政务网审批-互联网申报待处理 */
  hlwsbdclCount: number;
  /** 户籍信息-跨省协作 */
  hjxxKsxzCount: number;
  /** 户籍信息-跨省消息 */
  hjxxKsxxCount: number;
  hjCount?: number;
  total?: number;
  [key: string]: any;
}

/**
 * 获取代办工作统计
 * @param params
 */
export const apiTodoTaskStatistics = async (
  params?: any,
): Promise<TodoTaskStatisticsResponse> => {
  return requestClient.get('/dashboard/todoTaskStatistics', {
    params,
  });
};
