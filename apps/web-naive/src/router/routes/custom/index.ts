import type { RouteRecordStringComponent } from '@vben/types';

import type { MenuItem } from '#/api';

export const getAllRoutes: () => RouteRecordStringComponent[] = () => {
  return [
    {
      name: 'dashboard',
      path: '/dashboard',
      component: '/dashboard/index',
      meta: {
        order: 1,
        title: '工作台',
        accessCode: 'ignoreAccess',
      },
    },
    {
      // 这里固定写死 BasicLayout，不可更改
      component: 'BasicLayout',
      name: 'system',
      path: '/system',
      meta: {
        order: 2,
        title: '系统管理',
        accessCode: 'ignoreAccess',
      },
      children: [
        {
          name: 'safetyManagement',
          path: '/system/safetyManagement',
          meta: {
            title: '安全管理',
            accessCode: 'ignoreAccess',
          },
          children: [
            {
              name: 'userManagement',
              path: '/system/safetyManagement/userManagement',
              component: '/system/safetyManagement/userManagement/index',
              meta: {
                title: '用户管理',
                accessCode: 'ignoreAccess',
              },
            },
            {
              name: 'roleManagement',
              path: '/system/safetyManagement/roleManagement',
              component: '/system/safetyManagement/roleManagement/index',
              meta: {
                title: '角色管理',
                accessCode: 'ignoreAccess',
              },
            },
            {
              name: 'menuManagement',
              path: '/system/safetyManagement/menuManagement',
              component: '/system/safetyManagement/menuManagement/index',
              meta: {
                title: '菜单管理',
                accessCode: 'ignoreAccess',
              },
            },
          ],
        },
        {
          name: 'codeManagement',
          path: '/system/codeManagement',
          meta: {
            title: '系统码表',
            accessCode: 300000,
          },
          children: [
            {
              name: 'xtXtcsbManagement',
              path: '/system/codeManagement/xtXtcsbManagement',
              meta: {
                hideChildrenInMenu: true,
                title: '系统参数维护',
                accessCode: 300001,
              },
              children: [
                {
                  name: 'xtXtcsbPageType',
                  path: '',
                  component: '/system/codeManagement/xtXtcsbManagement/index',
                  meta: {
                    title: '系统参数维护',
                    accessCode: 300001,
                  },
                },
                {
                  name: 'xtXtcsbPage',
                  path: '/system/codeManagement/xtXtcsbManagement/page',
                  component: '/system/codeManagement/xtXtcsbManagement/page',
                  meta: {
                    activePath: '/system/codeManagement/xtXtcsbManagement',
                    title: '系统参数维护',
                    accessCode: 300001,
                  },
                },
              ],
            },
            {
              name: 'xtXzqhbManagement',
              path: '/system/codeManagement/xtXzqhbManagement',
              component: '/system/codeManagement/xtXzqhbManagement/index',
              meta: {
                title: '行政区划维护',
                accessCode: 300003,
              },
            },
            {
              name: 'xtDwxxbManagement',
              path: '/system/codeManagement/xtDwxxbManagement',
              component: '/system/safetyManagement/xtDwxxbManagement/index',
              meta: {
                title: '单位信息管理',
                accessCode: 300004,
              },
            },
            {
              name: 'xtXzjdxxbManagement',
              path: '/system/codeManagement/xtXzjdxxbManagement',
              component: '/system/codeManagement/xtXzjdxxbManagement/index',
              meta: {
                title: '乡镇街道维护',
                accessCode: 300006,
              },
            },
            {
              name: 'xtJwhxxbManagement',
              path: '/system/codeManagement/xtJwhxxbManagement',
              component: '/system/codeManagement/xtJwhxxbManagement/index',
              meta: {
                title: '居委会信息维护',
                accessCode: 300007,
              },
            },
            {
              name: 'xtJlxxxbManagement',
              path: '/system/codeManagement/xtJlxxxbManagement',
              component: '/system/codeManagement/xtJlxxxbManagement/index',
              meta: {
                title: '街路巷信息维护',
                accessCode: 300008,
              },
            },
            {
              name: 'xtDwjgdmbManagement',
              path: '/system/codeManagement/xtDwjgdmbManagement',
              component: '/system/codeManagement/xtDwjgdmbManagement/index',
              meta: {
                title: '公安机关机构代码维护',
                accessCode: 300011,
              },
            },
          ],
        },
        {
          name: 'controlManagement',
          path: '/system/controlManagement',
          meta: {
            title: '系统控制管理',
            accessCode: 310000,
          },
          children: [
            {
              name: 'xtXtkzcsbManagement',
              path: '/system/controlManagement/xtXtkzcsbManagement',
              component: '/system/controlManagement/xtXtkzcsbManagement/index',
              meta: {
                title: '控制参数维护',
                accessCode: 310001,
              },
            },
            {
              name: 'xtBssqbManagement',
              path: '/system/controlManagement/xtBssqbManagement',
              component: '/system/controlManagement/xtBssqbManagement/index',
              meta: {
                title: '本市市区维护',
                accessCode: 310004,
              },
            },
          ],
        },
      ],
    },
    {
      // 这里固定写死 BasicLayout，不可更改
      component: 'BasicLayout',
      name: 'business',
      path: '/business',
      meta: {
        order: 3,
        title: '业务模块',
        accessCode: 'ignoreAccess',
      },
      children: [
        {
          name: 'sjspsl',
          path: '/business/sjspsl',
          meta: {
            title: '省级审批受理',
            accessCode: 191100,
          },
          children: [
            {
              name: 'dzgxclcx',
              path: '/business/sjspsl/dzgxclcx',
              component: '/business/sjspsl/dzgxclcx/index',
              meta: {
                title: '电子共享材料查询',
                accessCode: 191107,
              },
            },
          ],
        },
        {
          name: 'sjzz',
          path: '/business/sjzz',
          meta: {
            title: '省级制证',
            accessCode: 'ignoreAccess',
          },
          children: [
            {
              name: 'sjzzsq',
              path: '/business/sjzz/sjzzsq',
              component: '/business/sjzz/sjzzsq/index',
              meta: {
                title: '省级制证申请',
                accessCode: 193002,
              },
            },
            // {
            //   name: 'sjzzsl',
            //   path: '/business/sjzz/sjzzsl',
            //   component: '/business/sjzz/sjzzsl/index',
            //   meta: {
            //     title: '省级制证受理',
            //     accessCode: 193004,
            //   },
            // },
          ],
        },
      ],
    },
    {
      name: 'Empty',
      path: '/empty',
      component: 'empty',
      meta: {
        title: 'Empty',
        hideInMenu: true,
        hideInBreadcrumb: true,
        hideInTab: true,
        accessCode: false,
      },
    },
    {
      component: 'FormDesign',
      meta: {
        accessCode: 'ignoreAccess',
        hideInBreadcrumb: true,
        hideInMenu: true,
        hideInTab: true,
        ignoreAccess: true,
        noBasicLayout: true,
        openInNewWindow: true,
        title: '表单设计器',
      },
      name: 'formDesign',
      path: '/form-design',
    },
    // generatorRoute as unknown as RouteRecordStringComponent,
  ];
};

export const filterRoutes = (
  allRoutes: RouteRecordStringComponent[],
  result: MenuItem[],
) => {
  const accessRoutes: RouteRecordStringComponent[] = [];
  // 递归检查是否存在 route.meta?.access属性 存在就存到accessRoutes,数据结构保持一致
  allRoutes.forEach((route) => {
    const { children, meta, ...rest } = route;
    const item = result.find((r) => r.gncdid === meta?.accessCode);
    if (meta?.accessCode === 'ignoreAccess') {
      accessRoutes.push({
        ...rest,
        meta: {
          ...meta,
          title: meta.title,
        },
        children: children ? filterRoutes(children, result) : undefined,
      });
    } else if (item) {
      accessRoutes.push({
        ...rest,
        meta: {
          ...meta,
          title: item.cdmc,
        },
        children: children ? filterRoutes(children, result) : undefined,
      });
    }
  });
  return accessRoutes;
};
