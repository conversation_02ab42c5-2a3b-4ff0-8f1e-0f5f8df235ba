<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd"
       default-lazy-init="true">

    <!--
    1、configId全局唯一
    2、bean.id也进行全局唯一设置
    3、exchangeServiceId为接口ID
    4、 JPG转化器：
        结构化：请用GaggsjPlatformDataStructConvertor
              urlPath=''
              structPath='_SELF' 即datas本身就是结构化数据
              如果需要结构化数据合并成一张图片，设置 mergeStruct2JpgListRunqian=/gaggsj/gaggsj_struct_pdf_export_merge.rpx，
              该参数会忽略struct2JpgRunqian的参数设置
        证照：请用GaggsjPlatformDataLicenceConvertor
              urlPath=ELC_LICENCE_FILE@URL
              structPath=ELC_LICENCE_STRUCT@DATA
              如果证照不需要结构化数据，请将structPath设置为空
    5、参数viewInvokeParamList 用于 公共web页面调用的表单字段
    -->
    <description>教育部高校学历姓名及证件号码查询接口</description>

    <bean id="gaggsjConfigParameterWGX33-00000971"
          class="com.zjjcnt.jk.wscall.gaggsj.config.GaggsjConfigParameterImpl">
        <!-- *********************************请求相关参数******************************** -->
        <!-- 接口配置全局唯一 -->
        <property name="configId" value="WGX33-00000971"/>

        <!-- 请求接口系统名称  -->
        <property name="invokeServiceName" value="${gaggsj.invoker.param.serviceName}"/>


        <!-- 接口服务ID. -->
        <property name="exchangeServiceId" value="WGX33-00000971"/>

        <!-- 接口服务名称 对应润乾标题. 对应 请求参数 Reg_ID -->
        <property name="exchangeServiceName" value="教育部高校学历姓名及证件号码查询接口"/>



        <!-- 请求地址. -->
        <property name="appUrl" value="${gaggsj.invoker.param.appUrl}"/>
        <!-- 请求应用ID. -->
        <property name="appId" value="${gaggsj.invoker.param.appId}"/>


        <!-- 主事项编码. -->
        <property name="powerMatters" value=""/>
        <!--  子事项编码. -->
        <property name="subPowerMatters" value=""/>

        <!-- ***************************公共页面查询相关***************************** -->
        <property name="viewInvokeParamList">
            <list>
                <bean class="com.zjjcnt.jk.wscall.gaggsj.config.ViewInvokeParam">
                    <property name="field" value="xm" />
                    <property name="chn" value="姓名" />
                </bean>
                <bean class="com.zjjcnt.jk.wscall.gaggsj.config.ViewInvokeParam">
                    <property name="field" value="zjhm" />
                    <property name="chn" value="证件号码" />
                </bean>
            </list>
        </property>
        <!-- ***************************sendData相关参数***************************** -->
        <!-- 默认实现 sendData生成器-->
        <property name="sendDataBuilder">
            <bean class="com.zjjcnt.jk.wscall.gaggsj.config.MapSendDataBuilerImpl">
                <!-- 必输项校验-->
                <property name="requiredField" value="xm,zjhm" />
                <!-- 接口其他参数 -->
                <property name="otherParamter">
                    <map>
                        <!--<entry key="other" value="1234456"></entry>-->
                    </map>
                </property>
            </bean>
        </property>

        <!--***************************结构化数据转化器*****************************-->
        <!--结构化数据转化-->
        <property name="dataConvertor">
            <bean class="com.zjjcnt.jk.wscall.gaggsj.convertor.GaggsjPlatformDataStructXmlConvertor">

                <!-- ******************************LICENCE证件照相关************************* -->
                <!-- url 证件照URL 空表示不获取URL，及无证件PDF文件URL-->
                <property name="urlPath" value="" />
                <!-- 自动将pdf下载后再次上传到OSS中 -->
                <property name="autoPdfUpload" value="true" />
                <!-- ******************************LICENCE证件照相关end ************************* -->

                <!-- ******************************结构化转化相关参数 根据structPath是否为空 判断是否处理结构化数据************************* -->
                <!-- 对应结构化数据 空将不再获取证件的结构化数据，如果有指定，请配置translaterList-->
                <property name="structPath" value="_SELF"></property>
                <!-- 指定润乾文件 -->
                <property name="struct2JpgRunqian" value="/gaggsj/gaggsj_struct_pdf_export.rpx"></property>
                <!-- 产生的润乾文件 的列数（名称+值 算一列） -->
                <property name="rqColumnNumber" value="2" />
                <!-- ******************************结构化转化相关参数 end ************************* -->


                <!-- ******************************pdf转jpg相关参数***************************** -->
                <!-- 根据width进行计算scale，将会忽略Pdf2JpgScale值 -->
                <property name="pdf2JpgWidth" value="0"/>

                <!-- pdf转jpg时的分辨率倍数 -->
                <property name="pdf2JpgScale" value="1" />

                <!-- pdf转jpg时的质量因子 -->
                <property name="pdf2JpgQuality" value="0.9"/>


                <!-- ******************************pdf转jpg相关参数 end***************************** -->



                <!-- 中文翻译 -->
                <property name="translaterList">
                    <list>

                        <bean class="com.zjjcnt.jk.wscall.gaggsj.convertor.StructTranslater">
                            <property name="field" value="xm"/>
                            <property name="chn" value="姓名"/>
                        </bean>
                        <bean class="com.zjjcnt.jk.wscall.gaggsj.convertor.StructTranslater">
                            <property name="field" value="zjhm"/>
                            <property name="chn" value="证件号码"/>
                        </bean>


                        <bean class="com.zjjcnt.jk.wscall.gaggsj.convertor.StructTranslater">
                            <property name="field" value="reqid"/>
                            <property name="chn" value="请求标识码"/>
                        </bean>

                        <bean class="com.zjjcnt.jk.wscall.gaggsj.convertor.StructTranslater">
                            <property name="field" value="yxmc"/>
                            <property name="chn" value="毕业学校名称"/>
                        </bean>


                        <bean class="com.zjjcnt.jk.wscall.gaggsj.convertor.StructTranslater">
                            <property name="field" value="zymc"/>
                            <property name="chn" value="专业名称"/>
                        </bean>
                        <bean class="com.zjjcnt.jk.wscall.gaggsj.convertor.StructTranslater">
                            <property name="field" value="cc"/>
                            <property name="chn" value="层次"/>
                        </bean>


                        <bean class="com.zjjcnt.jk.wscall.gaggsj.convertor.StructTranslater">
                            <property name="field" value="rxrq"/>
                            <property name="chn" value="入学日期"/>
                        </bean>

                        <bean class="com.zjjcnt.jk.wscall.gaggsj.convertor.StructTranslater">
                            <property name="field" value="byrq"/>
                            <property name="chn" value="毕业日期"/>
                        </bean>


                        <bean class="com.zjjcnt.jk.wscall.gaggsj.convertor.StructTranslater">
                            <property name="field" value="xxxs"/>
                            <property name="chn" value="学习形式"/>
                        </bean>
                        <bean class="com.zjjcnt.jk.wscall.gaggsj.convertor.StructTranslater">
                            <property name="field" value="zsbh"/>
                            <property name="chn" value="学历证书编号"/>
                        </bean>


                    </list>
                </property>

            </bean>
        </property>


    </bean>

</beans>