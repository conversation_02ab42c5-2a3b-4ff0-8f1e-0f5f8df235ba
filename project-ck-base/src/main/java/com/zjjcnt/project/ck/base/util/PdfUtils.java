package com.zjjcnt.project.ck.base.util;

import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.kernel.colors.DeviceGray;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * PDF生成工具类
 *
 * <AUTHOR>
 * @date 2025-07-11 15:01:53
 */
@Slf4j
public class PdfUtils {

    public static ByteArrayOutputStream generatePdfFromMap(Map<String, Object> dataMap, String title) {
        // 1. 创建 PDF writer 和 document
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             PdfWriter writer = new PdfWriter(baos);
             PdfDocument pdfDoc = new PdfDocument(writer);
             Document document = new Document(pdfDoc)) {


            // 2. 加载并设置中文字体（如黑体）
            ClassPathResource resource = new ClassPathResource("/font/chinese.stsong.ttf");
            byte[] byteArray = IOUtils.toByteArray(resource.getInputStream());
            PdfFont font = PdfFontFactory.createFont(byteArray, PdfEncodings.IDENTITY_H);

            // 3. 添加标题
            Paragraph titleParagraph = new Paragraph(title)
                    .setFont(font)
                    .setFontSize(14)
                    .setTextAlignment(TextAlignment.CENTER);
            document.add(titleParagraph);

            // 4. 创建 4 列表格
            Table table = new Table(4);
            table.setFont(font).setFontSize(12);
            // 设置表格宽度为页面宽度的90%
            table.setWidth(UnitValue.createPercentValue(90));
            // 设置表格居中
            table.setHorizontalAlignment(HorizontalAlignment.CENTER);

            // 5. 遍历 map，每两个字段组成一行
            List<Map.Entry<String, Object>> entries = new ArrayList<>(dataMap.entrySet());
            for (int i = 0; i < entries.size(); i += 2) {
                Map.Entry<String, Object> entry1 = entries.get(i);
                Cell keyCell1 = new Cell().add(new Paragraph(entry1.getKey()));
                keyCell1.setBackgroundColor(new DeviceGray(0.8f));
                table.addCell(keyCell1);
                table.addCell(getValue(entry1.getValue()));

                if (i + 1 < entries.size()) {
                    Map.Entry<String, Object> entry2 = entries.get(i + 1);
                    Cell keyCell2 = new Cell().add(new Paragraph(entry2.getKey()));
                    keyCell2.setBackgroundColor(new DeviceGray(0.8f)); // 浅灰色
                    table.addCell(keyCell2);
                    table.addCell(getValue(entry2.getValue()));
                } else {
                    // 奇数个字段时，补两个空单元格
                    table.addCell("");
                    table.addCell("");
                }
            }

            // 6. 添加表格到文档
            document.add(table);
            return baos;
        } catch (IOException e) {
            log.error("生成PDF文件失败", e);
        }
        return new ByteArrayOutputStream();
    }

    public static ByteArrayOutputStream generatePdfFromList(List<List<String>> datasetList, String title) {
        // 1. 创建 PDF writer 和 document
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             PdfWriter writer = new PdfWriter(baos);
             PdfDocument pdfDoc = new PdfDocument(writer);
             Document document = new Document(pdfDoc)) {


            // 2. 加载并设置中文字体（如黑体）
            ClassPathResource resource = new ClassPathResource("/font/chinese.stsong.ttf");
            byte[] byteArray = IOUtils.toByteArray(resource.getInputStream());
            PdfFont font = PdfFontFactory.createFont(byteArray, PdfEncodings.IDENTITY_H);

            // 3. 添加标题
            Paragraph titleParagraph = new Paragraph(title)
                    .setFont(font)
                    .setFontSize(14)
                    .setTextAlignment(TextAlignment.CENTER);
            document.add(titleParagraph);

            List<String> head = datasetList.getFirst();

            // 4. 创建 head.size() 列表格
            Table table = new Table(head.size());
            table.setFont(font).setFontSize(12);
            // 设置表格宽度为页面宽度的90%
            table.setWidth(UnitValue.createPercentValue(90));
            // 设置表格居中
            table.setHorizontalAlignment(HorizontalAlignment.CENTER);

            // 5. 遍历 list
            int rowIndex = 0;
            for (List<String> row : datasetList) {
                for (String value : row) {
                    if (rowIndex == 0) {
                        // 添加表头，加灰背景
                        Cell cell = new Cell().add(new Paragraph(value));
                        cell.setBackgroundColor(new DeviceGray(0.8f));
                        table.addCell(cell);
                    } else {
                        table.addCell(value);
                    }
                }
                rowIndex++;
            }

            // 6. 添加表格到文档
            document.add(table);
            return baos;
        } catch (IOException e) {
            log.error("生成PDF文件失败", e);
        }
        return new ByteArrayOutputStream();
    }

    private static String getValue(Object value) {
        if (value == null) {
            return "";
        }
        return value.toString();
    }
}