package com.zjjcnt.project.ck.base.web.controller;

import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;

/**
 * TODO description
 *
 * <AUTHOR>
 * @date 2025-07-16 14:37:00
 */
@RestController
@RequestMapping("/test")
public class TestController {

    private static final String PDF_FILE_PATH = "/Users/<USER>/workplace/project-ck3/output-it7.pdf";

    @GetMapping("/pdf")
    public ResponseEntity<InputStreamResource> viewPdf() throws FileNotFoundException {
        File file = new File(PDF_FILE_PATH);

        if (!file.exists()) {
            throw new RuntimeException("File not found: " + PDF_FILE_PATH);
        }

        FileInputStream fileInputStream = new FileInputStream(file);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", file.getName());

        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(file.length())
                .body(new InputStreamResource(fileInputStream));
    }
}