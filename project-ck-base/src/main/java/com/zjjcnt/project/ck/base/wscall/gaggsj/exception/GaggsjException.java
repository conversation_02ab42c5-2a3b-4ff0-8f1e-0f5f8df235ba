package com.zjjcnt.project.ck.base.wscall.gaggsj.exception;

public class GaggsjException
        extends RuntimeException {

    private Integer code;
    private String msg;

    public GaggsjException() {
    }

    public GaggsjException(String messge) {
        super(messge);
        this.msg = messge;
    }

    public GaggsjException(Integer code, String message) {
        super(message);
        this.code = code;
        this.msg = message;
    }

    public GaggsjException(Integer code, String message, Throwable t) {
        super(t);
        this.code = code;
        this.msg = message;
    }

    public GaggsjException(String message, Throwable t) {
        super(message, t);
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
