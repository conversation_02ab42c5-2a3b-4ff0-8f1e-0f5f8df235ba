package com.zjjcnt.project.ck.base.wscall.gaggsj.convertor;


import com.zjjcnt.project.ck.base.wscall.gaggsj.request.GaggsjInvokeResult;
import com.zjjcnt.project.ck.base.wscall.gaggsj.response.GaggsjPlatformResponse;

import java.util.List;

/**
 * 政务平台返回的数据 转化器
 * 1、如果是pdf格式的，直接返回pdf的url
 * 2、如果非pdf的结构化数据，返回List形式结构
 * Created by fudongwz on 2018/11/01.
 */
public interface GaggsjPlatformDataConvertor {

    String DATA_TYPE_STRUCT = "STRUCT";
    String DATA_TYPE_ELCLICENCEFILE = "ELC_LICENCE_FILE";

    /**
     * 是否结构化数据转化器
     */
    boolean isStruct();

    /**
     * 返回结构类型数据类型
     */
    String getDataType();


    /**
     * 返回转化过的结构化数据
     */
    List<Struct> getTranslateStructData(GaggsjPlatformResponse platformResponse);

    /**
     * 返回转化过的结构化数据 出生证副页 20210226 hubin
     */
    List<Struct> getTranslateStructDataCSZFY(GaggsjPlatformResponse platformResponse);

    /**
     * 返回转化过的结构化数据  民政部殡葬服务火化信息查询
     */
    List<Struct> getTranslateStructDataBzfwhhxxcx(GaggsjPlatformResponse platformResponse);


    /**
     * 结构化数据中文翻译器
     */
    List<StructTranslater> getTranslaterList();

    int getColumnNumber();//润乾图片的列数

    float getPdf2JpgScale();//pdf转jpg时的分辨率倍数

    int getPdf2JpgWidth();//根据width进行计算scale，将会忽略Pdf2JpgScale值

    float getPdf2JpgQuality();//pdf转jpg时的质量因子

    boolean isAutoPdfUpload();//是否自动下载后上传pdf文件到OSS中

    //是否需要格式化成一张jpg展现
    boolean isMergeStruct();

    //如果证照URL未空，是否根据结构化数据进行jpg的生成
    boolean isEmptyUrlUseStruct2Jpg();

    StructDataToImage getStructDataToImage(Struct struct, int structIndex, GaggsjInvokeResult invokeResult, boolean uploadpdf);
    StructDataToImage getStructDataToMergeImage(List<Struct> structList, GaggsjInvokeResult invokeResult, boolean uploadpdf);
}
