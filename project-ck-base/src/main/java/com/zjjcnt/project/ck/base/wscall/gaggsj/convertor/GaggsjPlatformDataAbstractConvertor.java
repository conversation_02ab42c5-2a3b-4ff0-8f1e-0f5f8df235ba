package com.zjjcnt.project.ck.base.wscall.gaggsj.convertor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.zjjcnt.common.core.utils.ApplicationContextHelper;
import com.zjjcnt.project.ck.base.manager.JkFileManager;
import com.zjjcnt.project.ck.base.util.PdfUtils;
import com.zjjcnt.project.ck.base.wscall.gaggsj.exception.GaggsjInvokeException;
import com.zjjcnt.project.ck.base.wscall.gaggsj.request.GaggsjInvokeResult;
import com.zjjcnt.project.ck.base.wscall.gaggsj.response.GaggsjPlatformResponse;
import com.zjjcnt.project.ck.base.wscall.gaggsj.util.GaggsjImageUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.net.URI;
import java.util.*;


public abstract class GaggsjPlatformDataAbstractConvertor implements GaggsjPlatformDataConvertor {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());
    /* ********************LICENCE证件照相关******************** */
    //url层级
    private String urlPath = "";//比如 ELC_LICENCE_FILE@URL
    //证件照LICENCE格式相关参数
    private boolean autoPdfUpload = true;//是否自动下载后上传pdf文件到OSS中
    private boolean emptyUrlUseStruct2Jpg = false;//如果证照URL未空，是否根据结构化数据进行jpg的生成
    /* ********************LICENCE证件照相关end******************** */


    /* ********************结构化数据相关******************** */
    //结构化层级：如果不指定，加不加载
    private String structPath = "";// 比如：_SELF ELC_LICENCE_STRUCT@DATA
    //结构化数据翻译器
    private List<StructTranslater> translaterList = Lists.newArrayList();
    //其他相关：pdf转jpg相关参数
    private int columnNumber = 1; //图片的列数


    //将所有结构化数据转化一张图片，如果有设置该值，则将按照List列表的形式进行展现
    private boolean mergeStruct = false;


    /* ********************结构化数据相关 end******************** */

    /* ********************PDF转JPG参数相关******************** */
    private float pdf2JpgScale = 2;//pdf转jpg时的分辨率倍数
    private int pdf2JpgWidth = 0;//根据width进行计算scale，将会忽略Pdf2JpgScale值
    private float pdf2JpgQuality = 0.9f;//pdf转jpg时的质量因子
    /* ********************PDF转JPG参数相关 end******************** */

    /**
     * 是否结构化数据转化器
     */
    @Override
    public boolean isStruct() {
        return GaggsjPlatformDataConvertor.DATA_TYPE_STRUCT.equals(this.getDataType());
    }


    /**
     * 获取结构化数据
     */
    public List<Struct> getTranslateStructData(GaggsjPlatformResponse platformResponse) {

        List<Map> orgiRootList = getOrigMapList(platformResponse);
        if (orgiRootList == null) {
            return null;
        }

        List<Struct> result = Lists.newArrayList();
        for (int index = 0; index < orgiRootList.size(); index++) {
            Map orgiRootMap = orgiRootList.get(index);
            String url = null;
            if (StringUtils.isNotBlank(this.getUrlPath())) {
                url = getUrl(orgiRootMap, this.getUrlPath());
            }
            Struct struct = new Struct(index, url);
            if (StringUtils.isNotBlank(this.getStructPath())) {
                //结构化数据
                if (StringUtils.isNotEmpty(this.getStructPath())) {
                    Map orgiDataMap = getStructMap(orgiRootMap, this.getStructPath());
                    List<StructTranslateData> structTranslateDataList = StructTranslater.translate(this.getTranslaterList(), orgiDataMap);
                    struct.addData(orgiDataMap, structTranslateDataList);
                }
            }
            result.add(struct);
        }
        return result;
    }

    /**
     * 获取结构化数据 出生证副页
     * 20210226 hubin
     */
    public List<Struct> getTranslateStructDataCSZFY(GaggsjPlatformResponse platformResponse) {
        Object ob = platformResponse.getDatas();
        if (!(ob instanceof JSON)) {
            return null;
        }

        JSONObject jsonRoot = ((JSONObject) ob).getJSONObject("root");
        JSONObject jsonRequest = jsonRoot.getJSONObject("request");
        JSONObject jsonBody = jsonRequest.getJSONObject("body");
        JSONObject jsonDataList = jsonBody.getJSONObject("dataList");
        JSONArray jsonDatas = jsonDataList.getJSONArray("data");

        List<Struct> result = Lists.newArrayList();
        for (int i = 0; i < jsonDatas.size(); i++) {
            JSONObject jsonData = jsonDatas.getJSONObject(i);
            String url = jsonData.getString("FILEURL");
            Struct struct = new Struct(i, url);
            result.add(struct);
        }
        return result;
    }

    /**
     * 获取结构化数据 民政部殡葬服务火化信息查询
     */
    public List<Struct> getTranslateStructDataBzfwhhxxcx(GaggsjPlatformResponse platformResponse) {
        Object ob = platformResponse.getDatas();
        if (!(ob instanceof String)) {
            return null;
        }

        JSONObject object = JSON.parseObject(ob.toString());
        JSONObject jsonResult = object.getJSONObject("result");
        // JSONObject jsonRequest = jsonResult.getJSONObject("request");
        JSONArray jsonDatas = jsonResult.getJSONArray("data");

        List<Map> orgiRootList = (List) jsonDatas;

        List<Struct> result = Lists.newArrayList();
        for (int index = 0; index < orgiRootList.size(); index++) {
            Map orgiRootMap = orgiRootList.get(index);

            Struct struct = new Struct(index, null);
            if (StringUtils.isNotBlank(this.getStructPath())) {
                //结构化数据
                if (StringUtils.isNotEmpty(this.getStructPath())) {
                    Map orgiDataMap = getStructMap(orgiRootMap, this.getStructPath());
                    List<StructTranslateData> structTranslateDataList = StructTranslater.translate(this.getTranslaterList(), orgiDataMap);
                    struct.addData(orgiDataMap, structTranslateDataList);
                }
            }
            result.add(struct);
        }
        return result;

    }


    /**
     * 指定一个原始map，及path路径
     * 得到一个url
     * 路径按照 ELC_LICENCE_FILE@URL 形式，区分大小写
     */
    protected String getUrl(Map orgiRootMap, String urlPath) {
        String[] urlPathArray = urlPath.split("@");
        Map tempMap = orgiRootMap;
        for (int i = 0; i < urlPathArray.length; i++) {
            if (tempMap == null) {
                throw new GaggsjInvokeException("证件照获取URL失败【" + urlPath + "】->" + urlPathArray[i] + "未空，无法进一步转化");
            }
            if (i != urlPathArray.length - 1) {
                tempMap = (Map) tempMap.get(urlPathArray[i]);
            }
            //最后一个，直接返回
            else {
                Object url = tempMap.get(urlPathArray[i]);
                return url == null ? null : String.valueOf(url);
            }
        }
        return null;
    }

    /**
     * 指定一个原始map，及path路径
     * 得到一个机构化数据
     * 路径按照 ELC_LICENCE_STRUCT@DATA 形式，区分大小写
     */
    protected Map getStructMap(Map orgiRootMap, String structPath) {
        //如果未指定或指定本身就是path路径
        if (structPath == null || "_SELF".equals(structPath)) {
            return orgiRootMap;
        }
        String[] structPathArray = structPath.split("@");
        Map tempMap = orgiRootMap;
        for (int i = 0; i < structPathArray.length; i++) {

            if (i != structPathArray.length - 1) {
                if (tempMap.get(structPathArray[i]) == null || !(tempMap.get(structPathArray[i]) instanceof Map)) {
                    throw new GaggsjInvokeException("结构获取失败【" + structPath + "】->" + structPathArray[i] + "未空，无法进一步转化");
                }
                tempMap = (Map) tempMap.get(structPathArray[i]);
            }
            //最后一个，直接返回
            else {
                Object data = tempMap.get(structPathArray[i]);
                if (data instanceof Map) {
                    return (Map) data;
                }
                throw new GaggsjInvokeException("结构获取失败【" + structPath + "】->" + structPathArray[i] + "非MAP数据");

            }
        }
        throw new GaggsjInvokeException("无法获取结构化数据");

    }


    /**
     * 获取MAP LIST 格式数据
     * platformResponse.datas可能是String类型的数据
     */
    protected List<Map> getOrigMapList(GaggsjPlatformResponse platformResponse) {
        //调用失败
        if (!platformResponse.isSucess()) {
            return null;
        }

        Object datas = platformResponse.getDatas();
        if (datas == null) {
            return null;
        }
        if (datas instanceof String && StringUtils.isEmpty((String) datas)) {
            return null;
        }

        List<Map> orgiRootList;
        if (datas instanceof String) {
            orgiRootList = (List) JSON.parseArray((String) datas);
        } else {
            orgiRootList = (List) datas;
        }
        return orgiRootList;
    }

    /**
     * 构造数据集，用于单独图片pdf
     */
    private Map<String, Object> createDataset(List<StructTranslateData> structTranslateDataList) {
        Map<String, Object> data = new LinkedHashMap<>();
        if (CollectionUtils.isEmpty(structTranslateDataList)) {
            return data;
        }
        structTranslateDataList.forEach(structTranslateData -> {
            String chn = StringUtils.isEmpty(structTranslateData.getChn()) ? "" : structTranslateData.getChn();
            data.put(chn, structTranslateData.getValue());
        });
        return data;
    }


    /**
     * 将所有的结构化数据作为一张图片
     */
    public List<List<String>> createMergeDataset(List<Struct> structList) {
        List<List<String>> datasetList = new ArrayList<>();
        if (CollectionUtils.isEmpty(structList)) {
            return datasetList;
        }

        List<StructTranslateData> firstTranslateDataList = structList.getFirst().getStructTranslateDataList();
        List<String> head = new ArrayList<>();
        head.add("序号");
        for (StructTranslateData structTranslateData : firstTranslateDataList) {
            head.add(structTranslateData.getChn());
        }

        int count = 0;
        for (Struct struct : structList) {
            if (!CollectionUtils.isEmpty(struct.getStructTranslateDataList())) {
                List<String> data = new ArrayList<>();
                data.add(String.valueOf(++count));
                for (StructTranslateData translateData : struct.getStructTranslateDataList()) {
                    data.add(translateData.getValue() == null ? "" : String.valueOf(translateData.getValue()));
                }
                datasetList.add(data);
            }
        }
        return datasetList;
    }

    /**
     * 获取结构化数据或PDF文件路径，得到JPG格式的数据
     *
     * @param struct       结构化数据
     * @param invokeResult 接口调用结果
     * @parm 所在结构化数组的indexx
     * @parm uploadpdf 是否上传pdf
     */
    public StructDataToImage getStructDataToImage(Struct struct, int structIndex, GaggsjInvokeResult invokeResult, boolean uploadpdf) {
        //将PDF转化为jpg格式
        byte[] jpg = null;
        byte[] pdf;
        try {
            //结构化数据获取jpg转化文件,证照数据如果URL未空，则判断EmptyUrlUseStruct2Jpg是否需要根据结构化数据转化
            if (invokeResult.isStruct() || (
                    !invokeResult.isStruct() //非结构化
                            && StringUtils.isEmpty(struct.getUrl())//证照URL未空
                            && invokeResult.getConfigParameter().getDataConvertor().isEmptyUrlUseStruct2Jpg()//根据结构化数据进行jpg的生成
            )
            ) {
                try {
                    //导出pdf
                    pdf = PdfUtils.generatePdfFromMap(this.createDataset(struct.getStructTranslateDataList()),
                            invokeResult.getConfigParameter().getExchangeServiceName()).toByteArray();

                    jpg = GaggsjImageUtils.convertPdf2Image(pdf,
                            this.getPdf2JpgWidth(),
                            this.getPdf2JpgScale(),
                            this.getPdf2JpgQuality());
                    return new StructDataToImage(invokeResult.getJkGaggsjLwcxqqrz().getNbbh(), structIndex, jpg);
                } catch (Exception e) {
                    throw new GaggsjInvokeException("生成公安公共数据图片出错：" + e.getMessage(), e);
                }
            } else {
                //证件照的pdf文件获取jpg及上传
                if (StringUtils.isEmpty(struct.getUrl())) {
                    return new StructDataToImage(invokeResult.getJkGaggsjLwcxqqrz().getNbbh(), structIndex, jpg);
                }
                /*
                 * 2023-08-01开始，公安公共数据平台的电子证照接口从oss地址转为 base64
                 * 所以这里做一个兼容，如果http开头的，就认为是oss地址，如果不是http开头，则认为是base64
                 * */
                if (struct.getUrl().toLowerCase().startsWith("http")) {
                    logger.info("RestTemplate开始下载：" + struct.getUrl());
                    RestTemplate gaggsjRestTemplate = ApplicationContextHelper.getBean(RestTemplate.class);

                    /**
                     * PDF的url其实已经进行编码，如果gaggsjRestTemplate.getForEntity(String,  byte[].class)方法默认再次进行编码
                     * 所以这里用 PDF的url其实已经进行编码，如果gaggsjRestTemplate.getForEntity(URI,  byte[].class)不再编码
                     * URI直接通过 URI.create（String）方法获取，否则通过UriComponentsBuilder获取的话，虽builder.build(true)设置为true，
                     * 如果URL的字符串中有测试字符比如 a=123=&b=123解析a时还是会报错
                     * */
                    URI uri = URI.create(struct.getUrl());
                    ResponseEntity<byte[]> result = gaggsjRestTemplate.getForEntity(uri, byte[].class);
                    pdf = result.getBody();
                    logger.info("RestTemplate下载文件大小：" + pdf.length);
                } else {
                    logger.debug("！！！！！！！！！！公安公共数据电子证照接口，为base64");
                    pdf = Base64.getDecoder().decode(struct.getUrl());
                }

                if (pdf == null || pdf.length == 0) {
                    throw new GaggsjInvokeException("获取【" + struct.getUrl() + "】结果为空");
                }

                jpg = GaggsjImageUtils.convertPdf2Image(pdf,
                        this.getPdf2JpgWidth(),
                        this.getPdf2JpgScale(),
                        this.getPdf2JpgQuality());
                StructDataToImage jpgData = new StructDataToImage(invokeResult.getJkGaggsjLwcxqqrz().getNbbh(), structIndex, jpg);
                //上传pdf文件
                if (uploadpdf) {
                    //保存到OSS中
                    JkFileManager jkFileManager = ApplicationContextHelper.getBean(JkFileManager.class);
                    String wjbh = jkFileManager.saveFileObject("gaggsj-licence", invokeResult.getJkGaggsjLwcxqqrz().getNbbh() + "-" + structIndex, pdf, "application/pdf",
                            invokeResult.getPlatformRequest().getSendData2StringMap(),
                            invokeResult.getJkGaggsjLwcxqqrz().getNbbh(),
                            structIndex + "",
                            null,//struct.getUrl(),
                            null,
                            null
                    );
                    jpgData.setOrgiDownloadWjbh(wjbh);

                }
                return jpgData;
            }
        } catch (IOException e) {
            logger.error("获取PDF->JPG失败" + e.getMessage(), e);
            throw new GaggsjInvokeException(e.getMessage(), e);
        }
    }

    public StructDataToImage getStructDataToMergeImage(List<Struct> structList, GaggsjInvokeResult invokeResult, boolean uploadpdf) {
        //设置数据
        if (structList == null || structList.isEmpty()) {
            return null;
        }

        try {
            //导出pdf
            byte[] pdf = PdfUtils.generatePdfFromList(this.createMergeDataset(structList),
                    invokeResult.getConfigParameter().getExchangeServiceName()).toByteArray();

            byte[] jpg = GaggsjImageUtils.convertPdf2Image(pdf,
                    this.getPdf2JpgWidth(),
                    this.getPdf2JpgScale(),
                    this.getPdf2JpgQuality());
            return new StructDataToImage(invokeResult.getJkGaggsjLwcxqqrz().getNbbh(), 0, jpg);
        } catch (Exception e) {
            throw new GaggsjInvokeException("生成公安公共数据图片出错：" + e.getMessage(), e);
        }
    }

    public String getUrlPath() {
        return urlPath;
    }

    public void setUrlPath(String urlPath) {
        this.urlPath = urlPath;
    }

    @Override
    public boolean isAutoPdfUpload() {
        return autoPdfUpload;
    }

    public void setAutoPdfUpload(boolean autoPdfUpload) {
        this.autoPdfUpload = autoPdfUpload;
    }

    public String getStructPath() {
        return structPath;
    }

    public void setStructPath(String structPath) {
        this.structPath = structPath;
    }

    @Override
    public List<StructTranslater> getTranslaterList() {
        return translaterList;
    }

    public void setTranslaterList(List<StructTranslater> translaterList) {
        this.translaterList = translaterList;
    }

    @Override
    public int getColumnNumber() {
        return columnNumber;
    }

    public void setColumnNumber(int columnNumber) {
        this.columnNumber = columnNumber;
    }

    @Override
    public float getPdf2JpgScale() {
        return pdf2JpgScale;
    }

    public void setPdf2JpgScale(float pdf2JpgScale) {
        this.pdf2JpgScale = pdf2JpgScale;
    }

    @Override
    public int getPdf2JpgWidth() {
        return pdf2JpgWidth;
    }

    public void setPdf2JpgWidth(int pdf2JpgWidth) {
        this.pdf2JpgWidth = pdf2JpgWidth;
    }

    @Override
    public float getPdf2JpgQuality() {
        return pdf2JpgQuality;
    }

    public void setPdf2JpgQuality(float pdf2JpgQuality) {
        this.pdf2JpgQuality = pdf2JpgQuality;
    }

    public void setMergeStruct(boolean mergeStruct) {
        this.mergeStruct = mergeStruct;
    }

    public boolean isMergeStruct() {
        return mergeStruct;
    }

    @Override
    public boolean isEmptyUrlUseStruct2Jpg() {
        return emptyUrlUseStruct2Jpg;
    }

    public void setEmptyUrlUseStruct2Jpg(boolean emptyUrlUseStruct2Jpg) {
        this.emptyUrlUseStruct2Jpg = emptyUrlUseStruct2Jpg;
    }
}
