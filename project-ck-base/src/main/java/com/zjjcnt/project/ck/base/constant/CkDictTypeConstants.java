package com.zjjcnt.project.ck.base.constant;

/**
 * 常口字典常量
 *
 * <AUTHOR>
 * @date 2024-04-10 09:40:00
 */
public final class CkDictTypeConstants {

    /**
     * 人员类别
     */
    public static final String DM_RYLB = "1006";

    /**
     * 户类型
     */
    public static final String DM_HLX = "1007";

    /**
     * 人员状态
     */
    public static final String DM_RYZT = "1010";

    /**
     * 迁入登记类别
     */
    public static final String DM_QRLB = "1020";

    /**
     * 冻结状态
     */
    public static final String DM_DJZT = "1111";

    /**
     * 人员锁定状态
     */
    public static final String DM_RYSDZT = "1050";

    /**
     * 变动原因
     */
    public static final String DM_BDYY = "1056";

    /**
     * 城乡属性
     */
    public static final String DM_CXSX = "1201";

    /**
     * 设备生产销售公司
     */
    public static final String DM_SBXSGS = "1280";

    /**
     * 申请人人像比对结果代码
     */
    public static final String DM_SQRRXBDJGDM = "1511";

    /**
     * 双重国籍业务批次状态
     */
    public static final String SCGJ_PCZT = "1521";

    /**
     * 双重国籍业务处理标志
     */
    public static final String SCGJ_CLBZ = "1522";

    /**
     * 双重国籍业务户籍数据来源
     */
    public static final String SCGJ_HJSJLY = "1523";

    /**
     * 双重国籍业务审核标志
     */
    public static final String SCGJ_SHBZ = "1524";

    /**
     * 双重国籍业务办结原因
     */
    public static final String SCGJ_BJYY = "1525";

    /**
     * 二代证制证类型
     */
    public static final String DM_EDZZZLX = "5008";

    /**
     * 二代证领证方式
     */
    public static final String DM_EDZLZFS = "5009";

    /**
     * 二代证受理状态
     */
    public static final String DM_EDZSLZT = "5011";

    /**
     * 制证信息错误类别
     */
    public static final String DM_ZZXXCWLB = "5017";

    /**
     * 受理方式
     */
    public static final String DM_SLFS = "5026";

    /**
     * 新的证件受理方式
     */
    public static final String DM_SLFS_NEW = "5027";

    /**
     * 不予受理类别
     */
    public static final String DM_BYSLLB = "5031";

    /**
     * 二代证收费类型(新)
     */
    public static final String DM_EDZSFLX = "5035";


    /**
     * 指位代码
     */
    public static final String DM_ZWDM = "5101";

    /**
     * 指纹注册结果代码
     */
    public static final String DM_ZWZCJGDM = "5103";

    /**
     * 指纹采集结果代码
     */
    public static final String DM_ZWCJJGDM = "5104";

    /**
     * 手指异常状况代码
     */
    public static final String DM_SZYCZKDM = "5105";

    /**
     * 二代证申领原因（部标）
     */
    public static final String DM_EDZSLYY = "7802";

    /**
     * 民族
     */
    public static final String DM_MZ = "8001";

    /**
     * 性别
     */
    public static final String DM_XB = "8003";

    /**
     * 家庭关系关系
     */
    public static final String DM_JTGX = "8006";

    /**
     * 国籍
     */
    public static final String GJ = "8008";


    /**
     * 收费业务类型
     */
    public static final String CKSFYWLX = "cksfywlx";

    /**
     * 参保地区业务代码
     */
    public static final String DM_CBDQYWDM = "cbdqywdm";

    private CkDictTypeConstants() {
    }
}
