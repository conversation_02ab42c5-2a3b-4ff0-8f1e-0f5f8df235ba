package com.zjjcnt.project.ck.base.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.project.ck.base.convert.JkWjJbConvert;
import com.zjjcnt.project.ck.base.dto.JkWjJbDTO;
import com.zjjcnt.project.ck.base.entity.JkWjJbDO;
import com.zjjcnt.project.ck.base.mapper.JkWjJbMapper;
import com.zjjcnt.project.ck.base.service.JkWjJbService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 文件基本信息ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-07-10 10:18:22
 */
@Service
public class JkWjJbServiceImpl extends AbstractBaseServiceImpl<JkWjJbMapper, JkWjJbDO, JkWjJbDTO> implements JkWjJbService {

    JkWjJbConvert convert = JkWjJbConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(JkWjJbDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(JkWjJbDO::getWjywmc, dto.getWjywmc(), StringUtils.isNotEmpty(dto.getWjywmc()));
        query.eq(JkWjJbDO::getWjywbh, dto.getWjywbh(), StringUtils.isNotEmpty(dto.getWjywbh()));
        query.eq(JkWjJbDO::getWjmc, dto.getWjmc(), StringUtils.isNotEmpty(dto.getWjmc()));
        query.eq(JkWjJbDO::getWjdx, dto.getWjdx(), Objects.nonNull(dto.getWjdx()));
        query.eq(JkWjJbDO::getWjlx, dto.getWjlx(), StringUtils.isNotEmpty(dto.getWjlx()));
        query.eq(JkWjJbDO::getWjsjdz, dto.getWjsjdz(), StringUtils.isNotEmpty(dto.getWjsjdz()));
        query.eq(JkWjJbDO::getHash, dto.getHash(), StringUtils.isNotEmpty(dto.getHash()));
        query.eq(JkWjJbDO::getFjsx, dto.getFjsx(), StringUtils.isNotEmpty(dto.getFjsx()));
        query.eq(JkWjJbDO::getLng, dto.getLng(), Objects.nonNull(dto.getLng()));
        query.eq(JkWjJbDO::getLat, dto.getLat(), Objects.nonNull(dto.getLat()));
        query.eq(JkWjJbDO::getAlt, dto.getAlt(), Objects.nonNull(dto.getAlt()));
        query.eq(JkWjJbDO::getCjrid, dto.getCjrid(), StringUtils.isNotEmpty(dto.getCjrid()));
        query.eq(JkWjJbDO::getCclx, dto.getCclx(), StringUtils.isNotEmpty(dto.getCclx()));
        query.eq(JkWjJbDO::getQtsx, dto.getQtsx(), StringUtils.isNotEmpty(dto.getQtsx()));
        query.eq(JkWjJbDO::getKzsx1, dto.getKzsx1(), StringUtils.isNotEmpty(dto.getKzsx1()));
        query.eq(JkWjJbDO::getKzsx2, dto.getKzsx2(), StringUtils.isNotEmpty(dto.getKzsx2()));
        query.eq(JkWjJbDO::getKzsx3, dto.getKzsx3(), StringUtils.isNotEmpty(dto.getKzsx3()));
        query.eq(JkWjJbDO::getKzsx4, dto.getKzsx4(), StringUtils.isNotEmpty(dto.getKzsx4()));
        query.eq(JkWjJbDO::getKzsx5, dto.getKzsx5(), StringUtils.isNotEmpty(dto.getKzsx5()));
        return query;
    }

    @Override
    public JkWjJbDTO convertToDTO(JkWjJbDO jkWjJbDO) {
        return convert.convert(jkWjJbDO);
    }

    @Override
    public JkWjJbDO convertToDO(JkWjJbDTO jkWjJbDTO) {
        return convert.convertToDO(jkWjJbDTO);
    }

    @Override
    public JkWjJbDTO insert(JkWjJbDTO dto) {
        dto.setLng(BigDecimal.ZERO);
        dto.setLat(BigDecimal.ZERO);
        dto.setAlt(BigDecimal.ZERO);
        return super.insert(dto);
    }

    @Override
    public List<JkWjJbDTO> findByWjywmcAndWjywbh(String wjywmc, String wjywbh) {
        JkWjJbDTO query = new JkWjJbDTO();
        query.setWjywmc(wjywmc);
        query.setWjywbh(wjywbh);
        return list(query);
    }
}
