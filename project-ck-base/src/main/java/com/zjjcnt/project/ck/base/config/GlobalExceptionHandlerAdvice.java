package com.zjjcnt.project.ck.base.config;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.exception.DefaultGlobalExceptionHandlerAdvice;
import com.zjjcnt.common.core.exception.UserErrorCode;
import com.zjjcnt.project.ck.base.exception.CkBaseErrorCode;
import com.zjjcnt.project.ck.base.wscall.gaggsj.exception.GaggsjException;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.resource.NoResourceFoundException;

/**
 * 错误拦截
 * <AUTHOR>
 * @date 2021/1/6
 */
@Hidden
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandlerAdvice extends DefaultGlobalExceptionHandlerAdvice {

    @ResponseStatus(HttpStatus.FORBIDDEN)
    @ExceptionHandler(AccessDeniedException.class)
    public CommonResult<Object> handleAccessDeniedException(AccessDeniedException e, HttpServletRequest request) {
        log.warn("[handleAccessDeniedException], url={}", request.getRequestURI(), e);
        return CommonResult.error(UserErrorCode.ACCESS_DENIED)
                .setDetailMessage(ExceptionUtil.getRootCauseMessage(e));
    }

    /**
     * 资源不存在
     */
    @ResponseStatus(HttpStatus.NOT_FOUND)
    @ExceptionHandler(NoResourceFoundException.class)
    public CommonResult<Object> handleNoResourceFoundException(NoResourceFoundException ex) {
        return CommonResult.error(UserErrorCode.USER_ERROR.getCode(), "请求地址不存在")
                .setDetailMessage(ExceptionUtils.getRootCauseMessage(ex));
    }

    @ExceptionHandler({GaggsjException.class})
    public CommonResult gaggsjExceptionHandler(GaggsjException ex) {
        log.error("[gaggsjExceptionHandler], message=[{}]", ex.getMessage());
        return CommonResult.error(CkBaseErrorCode.GET_GAGGSJ_ERROR.getCode(),
                ex.getMessage()).setDetailMessage(ex.getMessage());
    }
}
