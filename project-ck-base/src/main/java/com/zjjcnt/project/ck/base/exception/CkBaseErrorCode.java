package com.zjjcnt.project.ck.base.exception;

import com.zjjcnt.common.core.exception.ErrorCode;
import lombok.Getter;

/**
 * 常口错误代码
 *
 * <AUTHOR>
 * @date 2024-04-10 13:42:00
 */
@Getter
public enum CkBaseErrorCode implements ErrorCode {


    /**
     * 常口业务错误代码
     */
    SCGJPCB_ALREADY_EXIST(21001, "该国籍当日批次已存在"),
    SCGJPCB_ALREADY_FINISHED(21002, "该批次已采集完毕"),
    SCGJRY_ALREADY_EXIST(21003, "该批次人员已存在"),
    RKXX_NOT_FOUND(21004, "人口信息不存在"),
    SCGJPCB_NO_PERSON_CANNOT_COMMIT(21005, "未录入人员信息，不能提交批次"),
    SCGJPCB_NO_FILE_CANNOT_COMMIT(21006, "未上传使领馆函件，不能提交批次"),

    SQXX_CANNOT_COMMIT(22001, "证件业务申请无法提交"),
    SLXX_CANNOT_COMMIT(22002, "证件业务受理无法提交"),
    SQXX_NOT_FOUND(22003, "申请信息不存在"),
    SQXX_CANNOT_CANCEL(22004, "申请信息不允许执行不予受理操作"),
    BZSFLSF_CREATE_ERROR(22005, "新增办证收费流水表错误"),
    ZWTX_SAVE_ERROR(22006, "保存指纹图像数据发生错误!"),
    SLXX_NOT_FOUND(22007, "受理信息不存在!"),
    ZPLSB_NOT_FOUND(22008, "照片临时表不存在!"),
    ZWLSB_NOT_FOUND(22009, "指纹临时表不存在!"),
    ZWCJSBXX_ALREADY_EXIST(22010, "指纹采集设备已存在!"),
    ZWCJSBXX_NOT_FOUND(22011, "指纹采集设备不存在!"),
    HLWSQZT_UPDATE_FAILED(22012, "互联网状态修改失败!"),
    SLXX_AUDIT_ERROR(22013, "受理信息审核错误"),
    SLXX_ISSUE_ERROR(22014, "受理信息签发错误"),
    ZJT_RXXXB_ERROR(22015, "人像信息表错误"),
    JF_QRCODE_ERROR(22016, "缴费二维码错误"),
    SLXX_CS_ERROR(22017, "受理信息初审错误"),
    KSSFZ_ERROR(22018, "跨省身份证错误"),
    BGGZ_ERROR(22019, "变更更正信息错误"),
    RXBD_ERROR(22020, "人像比对错误"),
    QUERY_GAGGSJ_ERROR(22021, "查询公安公共数据失败"),


    //第三方错误
    GET_PERSON_INFO_ERROR(31001, "获取人口信息失败"),
    GET_GAGGSJ_ERROR(31002, "获取公安公共数据失败");

    /**
     * 错误类型码
     */
    private final Integer code;
    /**
     * 错误类型描述信息
     */
    private final String message;

    CkBaseErrorCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

}
