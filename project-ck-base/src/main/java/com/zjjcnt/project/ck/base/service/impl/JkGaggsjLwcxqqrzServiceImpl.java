package com.zjjcnt.project.ck.base.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.common.util.JsonUtils;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.base.convert.JkGaggsjLwcxqqrzConvert;
import com.zjjcnt.project.ck.base.dto.JkGaggsjLwcxqqrzDTO;
import com.zjjcnt.project.ck.base.entity.JkGaggsjLwcxqqrzDO;
import com.zjjcnt.project.ck.base.manager.JkFileManager;
import com.zjjcnt.project.ck.base.mapper.JkGaggsjLwcxqqrzMapper;
import com.zjjcnt.project.ck.base.service.JkGaggsjLwcxqqrzService;
import com.zjjcnt.project.ck.base.wscall.gaggsj.config.GaggsjConfigParameter;
import com.zjjcnt.project.ck.base.wscall.gaggsj.request.GaggsjInvokeRequest;
import com.zjjcnt.project.ck.base.wscall.gaggsj.request.GaggsjInvokeResult;
import com.zjjcnt.project.ck.base.wscall.gaggsj.request.GaggsjInvokeUserInfo;
import com.zjjcnt.project.ck.base.wscall.gaggsj.request.GaggsjPlatformRequest;
import com.zjjcnt.project.ck.base.wscall.gaggsj.response.GaggsjPlatformResponse;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.core.util.ServerTimeUtils;
import com.zjjcnt.project.ck.sysadmin.service.impl.ExBaseServiceImpl;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * 公共公共数据平台请求日志ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-07-09 15:44:48
 */
@RequiredArgsConstructor
@Service
public class JkGaggsjLwcxqqrzServiceImpl extends ExBaseServiceImpl<JkGaggsjLwcxqqrzMapper, JkGaggsjLwcxqqrzDO, JkGaggsjLwcxqqrzDTO> implements JkGaggsjLwcxqqrzService {

    JkGaggsjLwcxqqrzConvert convert = JkGaggsjLwcxqqrzConvert.INSTANCE;

    private final JkFileManager jkFileManager;

    @Override
    protected QueryWrapper genQueryWrapper(JkGaggsjLwcxqqrzDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(JkGaggsjLwcxqqrzDO::getAppId, dto.getAppId(), StringUtils.isNotEmpty(dto.getAppId()));
        query.eq(JkGaggsjLwcxqqrzDO::getExchangeServiceId, dto.getExchangeServiceId(), StringUtils.isNotEmpty(dto.getExchangeServiceId()));
        query.eq(JkGaggsjLwcxqqrzDO::getPowerMatters, dto.getPowerMatters(), StringUtils.isNotEmpty(dto.getPowerMatters()));
        query.eq(JkGaggsjLwcxqqrzDO::getSubPowerMatters, dto.getSubPowerMatters(), StringUtils.isNotEmpty(dto.getSubPowerMatters()));
        query.eq(JkGaggsjLwcxqqrzDO::getRequestUserName, dto.getRequestUserName(), StringUtils.isNotEmpty(dto.getRequestUserName()));
        query.eq(JkGaggsjLwcxqqrzDO::getRequestUserZjhm, dto.getRequestUserZjhm(), StringUtils.isNotEmpty(dto.getRequestUserZjhm()));
        query.eq(JkGaggsjLwcxqqrzDO::getRequestDeptCode, dto.getRequestDeptCode(), StringUtils.isNotEmpty(dto.getRequestDeptCode()));
        query.eq(JkGaggsjLwcxqqrzDO::getRegId, dto.getRegId(), StringUtils.isNotEmpty(dto.getRegId()));
        query.eq(JkGaggsjLwcxqqrzDO::getTerminalId, dto.getTerminalId(), StringUtils.isNotEmpty(dto.getTerminalId()));
        query.eq(JkGaggsjLwcxqqrzDO::getUserName, dto.getUserName(), StringUtils.isNotEmpty(dto.getUserName()));
        query.eq(JkGaggsjLwcxqqrzDO::getUserId, dto.getUserId(), StringUtils.isNotEmpty(dto.getUserId()));
        query.eq(JkGaggsjLwcxqqrzDO::getOrganization, dto.getOrganization(), StringUtils.isNotEmpty(dto.getOrganization()));
        query.eq(JkGaggsjLwcxqqrzDO::getOrganizationId, dto.getOrganizationId(), StringUtils.isNotEmpty(dto.getOrganizationId()));
        query.eq(JkGaggsjLwcxqqrzDO::getRequestSendData, dto.getRequestSendData(), StringUtils.isNotEmpty(dto.getRequestSendData()));
        query.eq(JkGaggsjLwcxqqrzDO::getSendDataGmsfhm, dto.getSendDataGmsfhm(), StringUtils.isNotEmpty(dto.getSendDataGmsfhm()));
        query.eq(JkGaggsjLwcxqqrzDO::getSendDataXm, dto.getSendDataXm(), StringUtils.isNotEmpty(dto.getSendDataXm()));
        query.eq(JkGaggsjLwcxqqrzDO::getRequestResultWjjb, dto.getRequestResultWjjb(), StringUtils.isNotEmpty(dto.getRequestResultWjjb()));
        query.ge(JkGaggsjLwcxqqrzDO::getRequestSendTime, dto.getRequestSendTimeStart(), StringUtils.isNotEmpty(dto.getRequestSendTimeStart()));
        query.le(JkGaggsjLwcxqqrzDO::getRequestSendTime, dto.getRequestSendTimeEnd(), StringUtils.isNotEmpty(dto.getRequestSendTimeEnd()));
        query.ge(JkGaggsjLwcxqqrzDO::getRequestEndTime, dto.getRequestEndTimeStart(), StringUtils.isNotEmpty(dto.getRequestEndTimeStart()));
        query.le(JkGaggsjLwcxqqrzDO::getRequestEndTime, dto.getRequestEndTimeEnd(), StringUtils.isNotEmpty(dto.getRequestEndTimeEnd()));
        query.eq(JkGaggsjLwcxqqrzDO::getResultCode, dto.getResultCode(), StringUtils.isNotEmpty(dto.getResultCode()));
        query.eq(JkGaggsjLwcxqqrzDO::getResultMsg, dto.getResultMsg(), StringUtils.isNotEmpty(dto.getResultMsg()));
        query.eq(JkGaggsjLwcxqqrzDO::getResultDataCount, dto.getResultDataCount(), Objects.nonNull(dto.getResultDataCount()));
        query.eq(JkGaggsjLwcxqqrzDO::getResultCode3rd, dto.getResultCode3rd(), StringUtils.isNotEmpty(dto.getResultCode3rd()));
        query.eq(JkGaggsjLwcxqqrzDO::getResultMsg3rd, dto.getResultMsg3rd(), StringUtils.isNotEmpty(dto.getResultMsg3rd()));
        query.eq(JkGaggsjLwcxqqrzDO::getResultCodeZw, dto.getResultCodeZw(), StringUtils.isNotEmpty(dto.getResultCodeZw()));
        query.eq(JkGaggsjLwcxqqrzDO::getResultMsgZw, dto.getResultMsgZw(), StringUtils.isNotEmpty(dto.getResultMsgZw()));
        query.eq(JkGaggsjLwcxqqrzDO::getInvokeStatus, dto.getInvokeStatus(), StringUtils.isNotEmpty(dto.getInvokeStatus()));
        query.eq(JkGaggsjLwcxqqrzDO::getInvokeError, dto.getInvokeError(), StringUtils.isNotEmpty(dto.getInvokeError()));
        query.eq(JkGaggsjLwcxqqrzDO::getInvokeSys, dto.getInvokeSys(), StringUtils.isNotEmpty(dto.getInvokeSys()));
        query.eq(JkGaggsjLwcxqqrzDO::getInvokeZjhm, dto.getInvokeZjhm(), StringUtils.isNotEmpty(dto.getInvokeZjhm()));
        query.eq(JkGaggsjLwcxqqrzDO::getInvokeId, dto.getInvokeId(), StringUtils.isNotEmpty(dto.getInvokeId()));
        query.eq(JkGaggsjLwcxqqrzDO::getInvokeMc, dto.getInvokeMc(), StringUtils.isNotEmpty(dto.getInvokeMc()));
        query.eq(JkGaggsjLwcxqqrzDO::getInvokeSzbmMc, dto.getInvokeSzbmMc(), StringUtils.isNotEmpty(dto.getInvokeSzbmMc()));
        query.eq(JkGaggsjLwcxqqrzDO::getInvokeSzbm, dto.getInvokeSzbm(), StringUtils.isNotEmpty(dto.getInvokeSzbm()));
        query.eq(JkGaggsjLwcxqqrzDO::getInvokeHost, dto.getInvokeHost(), StringUtils.isNotEmpty(dto.getInvokeHost()));
        query.eq(JkGaggsjLwcxqqrzDO::getCjrid, dto.getCjrid(), StringUtils.isNotEmpty(dto.getCjrid()));
        query.eq(JkGaggsjLwcxqqrzDO::getConfigId, dto.getConfigId(), StringUtils.isNotEmpty(dto.getConfigId()));
        query.eq(JkGaggsjLwcxqqrzDO::getInvokeServiceName, dto.getInvokeServiceName(), StringUtils.isNotEmpty(dto.getInvokeServiceName()));
        query.eq(JkGaggsjLwcxqqrzDO::getExchangeServiceName, dto.getExchangeServiceName(), StringUtils.isNotEmpty(dto.getExchangeServiceName()));
        return query;
    }

    @Override
    public JkGaggsjLwcxqqrzDTO convertToDTO(JkGaggsjLwcxqqrzDO jkGaggsjLwcxqqrzDO) {
        return convert.convert(jkGaggsjLwcxqqrzDO);
    }

    @Override
    public JkGaggsjLwcxqqrzDO convertToDO(JkGaggsjLwcxqqrzDTO jkGaggsjLwcxqqrzDTO) {
        return convert.convertToDO(jkGaggsjLwcxqqrzDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JkGaggsjLwcxqqrzDTO save(GaggsjInvokeResult result, GaggsjInvokeUserInfo invokeUserInfo, String beginTime, String endTime) {
        GaggsjConfigParameter configParameter = result.getConfigParameter();
        GaggsjPlatformRequest platformRequest = result.getPlatformRequest();
        JkGaggsjLwcxqqrzDTO qqrz = new JkGaggsjLwcxqqrzDTO();

        qqrz.setConfigId(configParameter.getConfigId());
        qqrz.setExchangeServiceName(configParameter.getExchangeServiceName());
        qqrz.setInvokeServiceName(configParameter.getInvokeServiceName());

        String appid;
        if (null == result.getInvokeRequest().getRequestMap().get("appid")) {
            appid = configParameter.getAppId();
        } else {
            appid = result.getInvokeRequest().getRequestMap().get("appid").toString();
        }
        qqrz.setAppId(appid);

        /* 接口服务ID.*/
        qqrz.setExchangeServiceId(configParameter.getExchangeServiceId());

        /* 主事项编码.*/
        qqrz.setPowerMatters(platformRequest.getPowerMatters());

        /* 子事项编码.*/
        qqrz.setSubPowerMatters(platformRequest.getSubPowerMatters());

        /* 请求人姓名.*/
        qqrz.setRequestUserName(platformRequest.getRequestUserName());

        /* 请求人证件号码.*/
        qqrz.setRequestUserZjhm(platformRequest.getRequestUserZjhm());

        /* 请求人所在部门编码.*/
        qqrz.setRequestDeptCode(platformRequest.getRequestDeptCode());

        /* 服务名称.*/
        qqrz.setRegId(platformRequest.getRegId()  );

        /* 终端标识（IP地址）.*/
        qqrz.setTerminalId(platformRequest.getTerminalId());

        /* 姓名.*/
        qqrz.setUserName(platformRequest.getUserName());

        /* 身份号码.*/
        qqrz.setUserId(platformRequest.getUserId());

        /* 公安机关机构代码名称.*/
        qqrz.setOrganization(platformRequest.getOrganization());

        /* 公安机关机构代码.*/
        qqrz.setOrganizationId(platformRequest.getOrganizationId());

        /* 数据包发送时间.*/
        qqrz.setRequestSendTime(beginTime);

        /* 数据包接收时间.*/
        qqrz.setRequestEndTime(endTime);


        /*
          请求参数相关
          */
        /* 请求参数（JSON）.*/
        qqrz.setRequestSendData(JsonUtils.toJsonString(platformRequest.getSendData()));

        GaggsjInvokeRequest invokeRequest = result.getInvokeRequest();
        /* 查询参数（身份号）.*/
        qqrz.setSendDataGmsfhm(invokeRequest.getGmsfhm());

        /* 查询参数（姓名）.*/
        qqrz.setSendDataXm(invokeRequest.getXm());

        /*
          请求结果
         */
        GaggsjPlatformResponse response = result.getPlatformResponse();

        /* 请求结果代码.*/
        qqrz.setResultCode(response.getCode());

        /* 请求结果描述.*/
        qqrz.setResultMsg(response.getMsg());

        /* 请求结果数量 */
        qqrz.setResultDataCount(response.getDataCount());

        /*是否调用成功*/
        qqrz.setInvokeStatus(response.isSucess() ? Constants.YES : Constants.NO);

        if (invokeUserInfo != null) {
            qqrz.setInvokeHost(invokeUserInfo.getInvokeHost());
            qqrz.setInvokeId(invokeUserInfo.getInvokeId());
            qqrz.setInvokeMc(invokeUserInfo.getInvokeMc());
            qqrz.setInvokeSys(invokeUserInfo.getInvokeSys());
            qqrz.setInvokeSzbm(invokeUserInfo.getInvokeSzbm());
            qqrz.setInvokeZjhm(invokeUserInfo.getInvokeZjhm());
            qqrz.setInvokeSzbmMc(invokeUserInfo.getInvokeSzbmMc());
        }
        qqrz = this.insert(qqrz);

        String wjbh = jkFileManager.saveFileObject("gaggsj-qqjg", qqrz.getNbbh(),
                result.getPlatformJsonResult().getBytes(StandardCharsets.UTF_8), "application/json",
                result.getPlatformRequest().getSendData2StringMap());

        /* 请求参数OSS或DB.*/
        qqrz.setRequestResultWjjb(wjbh);

        update(qqrz);
        return qqrz;
    }

    @Override
    protected void beforeInsert(JkGaggsjLwcxqqrzDTO dto) {
        CustomUserDetails currentUser = SecurityUtils.getCurrentUser();
        dto.setCjr(currentUser.getName());
        dto.setCjsj(ServerTimeUtils.getCurrentTime());
        dto.setCjrip(currentUser.getRemoteAddress());
        dto.setCjrid(String.valueOf(currentUser.getUserId()));
        super.beforeInsert(dto);
    }
}
