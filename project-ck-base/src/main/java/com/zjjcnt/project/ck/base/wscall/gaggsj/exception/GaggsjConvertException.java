package com.zjjcnt.project.ck.base.wscall.gaggsj.exception;

public class GaggsjConvertException
        extends GaggsjException {

    public GaggsjConvertException(String messge) {
        super(messge);

    }

    public GaggsjConvertException() {
    }

    public GaggsjConvertException(Integer code, String message) {

        super(code, message);
    }

    public GaggsjConvertException(Integer code, String message, Throwable t) {
        super(code, message, t);
    }

    public GaggsjConvertException(String message, Throwable t) {
        super(message, t);
    }

}
