package com.zjjcnt.project.ck.base.wscall.gaggsj.exception;

public class GaggsjInvokeException
        extends GaggsjException {

    public GaggsjInvokeException(String messge) {
        super(messge);

    }

    public GaggsjInvokeException() {
    }

    public GaggsjInvokeException(Integer code, String message) {

        super(code, message);
    }

    public GaggsjInvokeException(Integer code, String message, Throwable t) {
        super(code, message, t);
    }

    public GaggsjInvokeException(String message, Throwable t) {
        super(message, t);
    }

}
