package com.zjjcnt.project.ck.base.util;

import com.zjjcnt.project.ck.sysadmin.dto.XtDwxxbDTO;
import org.junit.jupiter.api.Test;

import java.util.Optional;

/**
 * TODO description
 *
 * <AUTHOR>
 * @date 2025-03-18 15:15:00
 */
class DhhmUtilsTest {

    @Test
    public void main22() {

        String w = "7B09226D7367223A2022E68890E58A9F222C0922636F6465223A20223030222C092264617461223A2022222C09226461746173223A205B7B0909226D436172644964223A2022333632333330313939313033303337313358222C090922726567697374726174696F6E4F7267223A2022E69DADE5B79EE5B882E4B88AE59F8EE58CBAE6B091E694BFE5B180E5A99AE5A7BBE799BBE8AEB0E5A484222C090922664E616D65223A2022E891A3E4BDB3E88E89222C090922726567697374726174696F6E44617465223A2022323031372D30352D3331222C0909226964223A20223138353939373133222C090922627573696E65737354797065223A2022E7BB93E5A99AE799BBE8AEB0222C0909226D4E616D65223A2022E78E8BE5A881222C09092266436172644964223A2022333330313032313939343038313831383235222C090922746F6E675F74696D65223A2022323031382D30362D31382032303A35333A32372E3022097D5D2C0922726571756573744964223A20223363646464336662353133303439323038316661323564636433353161303438222C092264617461436F756E74223A20317D";
        byte[] bytes = hexStringToBytes(w);
        String b = new String(bytes);
        System.out.println(b);
    }


    /**
     * 将16进制字符串转换为byte数组
     *
     * @param hexString 16进制字符串（不带0x前缀）
     * @return byte数组
     */
    public static byte[] hexStringToBytes(String hexString) {
        if (hexString == null || hexString.isEmpty()) {
            return new byte[0];
        }

        int len = hexString.length();
        if (len % 2 != 0) {
            throw new IllegalArgumentException("Hex string must have even length");
        }

        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                    + Character.digit(hexString.charAt(i + 1), 16));
        }
        return data;
    }


    @Test
    public void a() {
        System.out.println(DhhmUtils.changeDhhm("1939218933,189232344,12329-939,jfiej1123"));
    }

    @Test
    public void b() {
//        XtDwxxbDTO dto = null;
        XtDwxxbDTO dto = new XtDwxxbDTO();
        dto.setMc("");
        String s = Optional.ofNullable(dto).map(XtDwxxbDTO::getMc).orElse("1");
        System.out.println("mc=" + s);
    }

}